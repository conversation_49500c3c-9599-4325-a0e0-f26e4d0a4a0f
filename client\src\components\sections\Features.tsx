import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { InfoIcon, ArrowRight } from "lucide-react";
import FeatureInfo from "./FeatureInfo";

// Import improved anime-style feature images with futuristic pesantren tech
import assessmentImage from "@assets/generated_images/Anime_digital_platform_separate_8fac5857.png";
import educationImage from "@assets/generated_images/Anime_educational_resources_tech_0a3c4da5.png";
import gamesImage from "@assets/generated_images/Anime_interactive_games_futuristic_f1f714b4.png";
import analyticsImage from "@assets/generated_images/Anime_analytics_futuristic_tech_4639e93e.png";

const Features = () => {
  const items = [
    {
      title: "Assessment Terstruktur",
      desc: "Kuesioner valid: MHKQ, PDD, GSE, MSCS, SRQ-20, DASS-42.",
      image: assessmentImage,
      key: "assessment" as const,
      gradient: "from-blue-500/10 to-indigo-500/10",
      iconColor: "text-blue-600",
      borderColor: "border-blue-200/50",
      stats: "6 Tools",
    },
    {
      title: "Rekomendasi Terapeutik",
      desc: "Perawatan diri dan modifikasi perilaku terarah.",
      image: educationImage,
      key: "therapeutic" as const,
      gradient: "from-emerald-500/10 to-green-500/10",
      iconColor: "text-emerald-600",
      borderColor: "border-emerald-200/50",
      stats: "Personalized",
    },
    {
      title: "Edukasi Interaktif",
      desc: "E-modul, video, game anti-bullying, dan resource lainnya.",
      image: gamesImage,
      key: "education" as const,
      gradient: "from-purple-500/10 to-pink-500/10",
      iconColor: "text-purple-600",
      borderColor: "border-purple-200/50",
      stats: "Multi-Media",
    },
    {
      title: "Analytics & Riwayat",
      desc: "Lihat progres dari waktu ke waktu (coming soon).",
      image: analyticsImage,
      key: "analytics" as const,
      gradient: "from-orange-500/10 to-red-500/10",
      iconColor: "text-orange-600",
      borderColor: "border-orange-200/50",
      stats: "Coming Soon",
    },
  ];

  return (
    <section id="features" className="container mx-auto px-6 py-16 md:py-24 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent pointer-events-none" />

      <div className="relative z-10">
        {/* Enhanced Header with Better Typography */}
        <header className="text-center mb-20 space-y-6">
          <div className="inline-flex items-center px-6 py-3 rounded-full glass bg-primary/10 text-primary text-base font-medium mb-6 shadow-glass hover-lift transition-all duration-300">
            <div className="w-3 h-3 bg-primary rounded-full mr-3 animate-pulse-glow" />
            <span className="font-heading">Fitur Unggulan</span>
          </div>
          <h2 className="space-y-3">
            <div className="font-display text-4xl md:text-6xl lg:text-7xl font-black">
              <span className="text-gradient">Solusi Komprehensif</span>
            </div>
            <div className="font-heading text-2xl md:text-4xl lg:text-5xl font-semibold text-foreground/80">
              untuk Kesehatan Mental Santri
            </div>
          </h2>
          <p className="font-body text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Dirancang <span className="font-semibold text-foreground">mobile-first</span> dengan UI/UX modern, responsif, dan intuitif untuk memberikan pengalaman terbaik.
          </p>
        </header>

        <Separator className="mb-12" />

        {/* Enhanced Cards Grid with Varied Layouts */}
        <div className="grid gap-10 sm:grid-cols-2 lg:grid-cols-2">
          {items.map((f, index) => (
            <Card
              key={f.title}
              className={`card-enhanced group overflow-hidden border-2 ${f.borderColor} glass-card bg-gradient-to-br ${f.gradient} shadow-elegant hover:shadow-glow hover:-translate-y-4 transition-all duration-700 stagger-item ${
                index % 2 === 0 ? 'lg:rotate-1 hover:rotate-0' : 'lg:-rotate-1 hover:rotate-0'
              } ${index === 0 || index === 3 ? 'lg:scale-105' : ''}`}
              style={{
                animationDelay: `${index * 200}ms`,
                transformOrigin: 'center center'
              }}
            >
              {/* Enhanced Feature Image with Multiple Overlays */}
              <div className="relative w-full h-64 overflow-hidden rounded-t-xl">
                <img
                  src={f.image}
                  alt={f.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-all duration-700 ease-out filter group-hover:brightness-110"
                />

                {/* Multiple Overlay Effects */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
                <div className={`absolute inset-0 bg-gradient-to-br ${f.gradient} opacity-20 group-hover:opacity-30 transition-opacity duration-500`} />

                {/* Animated Shimmer Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />

                {/* Enhanced Stats Badge with Glassmorphism */}
                <div className="absolute top-4 right-4 glass rounded-full px-3 py-1 shadow-glass hover-lift transition-all duration-300">
                  <span className={`text-sm font-bold ${f.iconColor}`}>{f.stats}</span>
                </div>

                {/* Enhanced Hover Overlay */}
                <div className="absolute inset-0 bg-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <div className="glass rounded-full p-4 transform scale-75 group-hover:scale-100 transition-transform duration-300 shadow-glass">
                    <InfoIcon className={`h-8 w-8 ${f.iconColor}`} />
                  </div>
                </div>
              </div>

              {/* Enhanced Card Content with Better Typography */}
              <div className="p-8 space-y-6">
                <CardHeader className="p-0">
                  <CardTitle className="font-heading text-2xl font-bold group-hover:text-primary transition-colors duration-500 leading-tight">
                    {f.title}
                  </CardTitle>
                </CardHeader>

                <CardContent className="p-0 space-y-6">
                  <p className="font-body text-lg text-muted-foreground leading-relaxed">
                    {f.desc}
                  </p>

                  {/* Enhanced CTA Button with Advanced Interactions */}
                  <FeatureInfo
                    featureKey={f.key}
                    trigger={
                      <Button
                        variant="outline"
                        size="lg"
                        className={`btn-enhanced group/btn gap-3 w-full border-2 ${f.borderColor} hover:bg-gradient-to-r ${f.gradient} hover:border-primary/80 transition-all duration-500 backdrop-blur-sm min-h-[var(--touch-target-comfortable)] rounded-xl font-semibold text-base`}
                      >
                        <InfoIcon className="h-5 w-5 transition-all duration-500 group-hover/btn:scale-125 group-hover/btn:rotate-12" />
                        <span className="font-heading">Pelajari Lebih Lanjut</span>
                        <ArrowRight className="h-5 w-5 group-hover/btn:translate-x-2 group-hover/btn:scale-125 transition-all duration-500" />
                      </Button>
                    }
                  />
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
