import React from 'react';
import { cn } from '@/lib/utils';
import { CheckCircle, Circle, Clock, AlertCircle } from 'lucide-react';

interface ProgressIndicatorProps {
  steps: Array<{
    id: string;
    title: string;
    description?: string;
    status: 'completed' | 'current' | 'pending' | 'error';
  }>;
  className?: string;
  variant?: 'horizontal' | 'vertical';
  showConnectors?: boolean;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  steps,
  className,
  variant = 'horizontal',
  showConnectors = true
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-emerald-600" />;
      case 'current':
        return <Clock className="w-5 h-5 text-primary animate-pulse" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-destructive" />;
      default:
        return <Circle className="w-5 h-5 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-emerald-100 border-emerald-300 text-emerald-800';
      case 'current':
        return 'bg-primary/10 border-primary text-primary';
      case 'error':
        return 'bg-destructive/10 border-destructive text-destructive';
      default:
        return 'bg-muted border-muted-foreground/20 text-muted-foreground';
    }
  };

  if (variant === 'vertical') {
    return (
      <div className={cn("space-y-4", className)}>
        {steps.map((step, index) => (
          <div key={step.id} className="relative">
            <div className="flex items-start gap-4">
              <div className={cn(
                "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300",
                getStatusColor(step.status)
              )}>
                {getStatusIcon(step.status)}
              </div>
              <div className="flex-1 min-w-0">
                <h3 className={cn(
                  "font-medium transition-colors duration-300",
                  step.status === 'completed' ? 'text-emerald-800' :
                  step.status === 'current' ? 'text-primary' :
                  step.status === 'error' ? 'text-destructive' :
                  'text-muted-foreground'
                )}>
                  {step.title}
                </h3>
                {step.description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {step.description}
                  </p>
                )}
              </div>
            </div>
            {showConnectors && index < steps.length - 1 && (
              <div className="absolute left-5 top-10 w-0.5 h-6 bg-border" />
            )}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("flex items-center justify-between", className)}>
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <div className="flex flex-col items-center text-center max-w-32">
            <div className={cn(
              "flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 mb-2",
              getStatusColor(step.status)
            )}>
              {getStatusIcon(step.status)}
            </div>
            <h3 className={cn(
              "text-sm font-medium transition-colors duration-300",
              step.status === 'completed' ? 'text-emerald-800' :
              step.status === 'current' ? 'text-primary' :
              step.status === 'error' ? 'text-destructive' :
              'text-muted-foreground'
            )}>
              {step.title}
            </h3>
            {step.description && (
              <p className="text-xs text-muted-foreground mt-1">
                {step.description}
              </p>
            )}
          </div>
          {showConnectors && index < steps.length - 1 && (
            <div className="flex-1 h-0.5 bg-border mx-4" />
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

// Circular Progress Component
interface CircularProgressProps {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  className?: string;
  color?: 'primary' | 'accent' | 'emerald' | 'blue';
}

export const CircularProgress: React.FC<CircularProgressProps> = ({
  value,
  max = 100,
  size = 'md',
  showValue = true,
  className,
  color = 'primary'
}) => {
  const percentage = Math.min((value / max) * 100, 100);
  const circumference = 2 * Math.PI * 45; // radius = 45
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const colorClasses = {
    primary: 'stroke-primary',
    accent: 'stroke-accent',
    emerald: 'stroke-emerald-600',
    blue: 'stroke-blue-600'
  };

  return (
    <div className={cn("relative inline-flex items-center justify-center", sizeClasses[size], className)}>
      <svg className="transform -rotate-90 w-full h-full">
        <circle
          cx="50%"
          cy="50%"
          r="45%"
          stroke="currentColor"
          strokeWidth="8"
          fill="transparent"
          className="text-muted/20"
        />
        <circle
          cx="50%"
          cy="50%"
          r="45%"
          stroke="currentColor"
          strokeWidth="8"
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn("transition-all duration-500 ease-out", colorClasses[color])}
        />
      </svg>
      {showValue && (
        <div className={cn(
          "absolute inset-0 flex items-center justify-center font-bold",
          textSizeClasses[size],
          colorClasses[color].replace('stroke-', 'text-')
        )}>
          {Math.round(percentage)}%
        </div>
      )}
    </div>
  );
};

// Linear Progress Bar
interface LinearProgressProps {
  value: number;
  max?: number;
  className?: string;
  showValue?: boolean;
  color?: 'primary' | 'accent' | 'emerald' | 'blue';
  animated?: boolean;
}

export const LinearProgress: React.FC<LinearProgressProps> = ({
  value,
  max = 100,
  className,
  showValue = false,
  color = 'primary',
  animated = true
}) => {
  const percentage = Math.min((value / max) * 100, 100);

  const colorClasses = {
    primary: 'bg-primary',
    accent: 'bg-accent',
    emerald: 'bg-emerald-600',
    blue: 'bg-blue-600'
  };

  return (
    <div className={cn("w-full", className)}>
      <div className="flex justify-between items-center mb-2">
        {showValue && (
          <span className="text-sm font-medium text-foreground">
            {Math.round(percentage)}%
          </span>
        )}
      </div>
      <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
        <div
          className={cn(
            "h-full rounded-full transition-all duration-500 ease-out",
            colorClasses[color],
            animated && "animate-pulse"
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

export default ProgressIndicator;
