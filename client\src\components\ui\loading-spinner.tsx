import React from 'react';
import { cn } from '@/lib/utils';
import { <PERSON>ader2, <PERSON>, Heart, Sparkles } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'dots' | 'pulse' | 'brain' | 'heart' | 'sparkles';
  className?: string;
  text?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  className,
  text
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  };

  const renderSpinner = () => {
    switch (variant) {
      case 'dots':
        return (
          <div className="flex space-x-1">
            <div className={cn("rounded-full bg-primary animate-bounce", sizeClasses[size])} style={{ animationDelay: '0ms' }} />
            <div className={cn("rounded-full bg-primary animate-bounce", sizeClasses[size])} style={{ animationDelay: '150ms' }} />
            <div className={cn("rounded-full bg-primary animate-bounce", sizeClasses[size])} style={{ animationDelay: '300ms' }} />
          </div>
        );
      
      case 'pulse':
        return (
          <div className={cn("rounded-full bg-gradient-to-r from-primary to-accent animate-pulse", sizeClasses[size])} />
        );
      
      case 'brain':
        return (
          <Brain className={cn("text-primary animate-spin", sizeClasses[size])} />
        );
      
      case 'heart':
        return (
          <Heart className={cn("text-red-500 animate-heartbeat", sizeClasses[size])} />
        );
      
      case 'sparkles':
        return (
          <Sparkles className={cn("text-accent animate-wiggle", sizeClasses[size])} />
        );
      
      default:
        return (
          <Loader2 className={cn("animate-spin text-primary", sizeClasses[size])} />
        );
    }
  };

  return (
    <div className={cn("flex flex-col items-center justify-center gap-2", className)}>
      {renderSpinner()}
      {text && (
        <p className={cn("text-muted-foreground font-medium animate-pulse", textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  );
};

// Skeleton Loading Component
interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular' | 'card';
}

export const Skeleton: React.FC<SkeletonProps> = ({ className, variant = 'rectangular' }) => {
  const baseClasses = "animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200px_100%] animate-skeleton-loading";
  
  const variantClasses = {
    text: "h-4 rounded",
    circular: "rounded-full aspect-square",
    rectangular: "rounded-md",
    card: "rounded-lg h-32"
  };

  return (
    <div className={cn(baseClasses, variantClasses[variant], className)} />
  );
};

// Enhanced Loading States
export const LoadingCard: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("p-6 space-y-4 border rounded-lg", className)}>
    <Skeleton variant="text" className="w-3/4" />
    <Skeleton variant="text" className="w-1/2" />
    <Skeleton variant="rectangular" className="h-20" />
    <div className="flex space-x-2">
      <Skeleton variant="circular" className="w-8 h-8" />
      <Skeleton variant="text" className="flex-1" />
    </div>
  </div>
);

export const LoadingGrid: React.FC<{ items?: number; className?: string }> = ({ 
  items = 6, 
  className 
}) => (
  <div className={cn("grid gap-6 sm:grid-cols-2 lg:grid-cols-3", className)}>
    {Array.from({ length: items }).map((_, i) => (
      <LoadingCard key={i} />
    ))}
  </div>
);

// Page Loading Component
export const PageLoading: React.FC<{ text?: string }> = ({ text = "Memuat..." }) => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center space-y-4">
      <LoadingSpinner size="xl" variant="brain" />
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-foreground">{text}</h2>
        <p className="text-muted-foreground">Mohon tunggu sebentar</p>
      </div>
    </div>
  </div>
);

export default LoadingSpinner;
