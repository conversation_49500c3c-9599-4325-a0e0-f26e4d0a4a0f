import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import MediaLightbox, { MediaKind } from "@/components/media/MediaLightbox";
import { useState } from "react";
import { BookOpen, Play, FileText, Gamepad2, Video, Download, ExternalLink, Clock, Users } from "lucide-react";

const Education = () => {
  const items: {
    title: string;
    link: string;
    kind: MediaKind;
    description: string;
    duration: string;
    category: string;
    icon: React.ComponentType<any>;
    gradient: string;
    stats: string;
  }[] = [
    {
      title: "E-Modul Psikoedukasi Kesehatan Jiwa",
      link: "https://drive.google.com/file/d/1J9Me3jFdIvwWZFfCzgyoCrHbEk2/view?usp=sharing",
      kind: "pdf",
      description: "Panduan komprehensif tentang kesehatan jiwa dalam perspektif Islam dan psikologi modern.",
      duration: "30 menit",
      category: "Modul",
      icon: BookOpen,
      gradient: "from-blue-500/10 to-indigo-500/10",
      stats: "PDF Guide",
    },
    {
      title: "Modul Perawatan Diri Kesehatan Jiwa di Pesantren",
      link: "https://drive.google.com/file/d/1tyJU--saXDw5gARV7msA4voQCziVxD4W/view?usp=sharing",
      kind: "pdf",
      description: "Strategi praktis untuk menjaga kesehatan mental dalam lingkungan pesantren.",
      duration: "25 menit",
      category: "Panduan",
      icon: FileText,
      gradient: "from-emerald-500/10 to-green-500/10",
      stats: "Self-Care",
    },
    {
      title: "Game Online Pencegahan Bullying - GEN ZAS",
      link: "https://itch.io/embed/2923060",
      kind: "iframe",
      description: "Game interaktif untuk memahami dan mencegah bullying di lingkungan pesantren.",
      duration: "15 menit",
      category: "Game",
      icon: Gamepad2,
      gradient: "from-purple-500/10 to-pink-500/10",
      stats: "Interactive",
    },
    {
      title: "Film Psikoedukasi – Sesi 1",
      link: "https://drive.google.com/file/d/1iRiig-oZncFO20XThcdQ_r588YfsAIXe/view?usp=sharing",
      kind: "video",
      description: "Video edukasi tentang dasar-dasar kesehatan mental untuk santri.",
      duration: "12 menit",
      category: "Video",
      icon: Video,
      gradient: "from-orange-500/10 to-red-500/10",
      stats: "Episode 1",
    },
    {
      title: "Film Psikoedukasi – Sesi 2",
      link: "https://drive.google.com/file/d/1avTWpPYSWu6Ldu0pQKhk5ML5TLA6jDaR/view?usp=sharing",
      kind: "video",
      description: "Lanjutan video edukasi dengan fokus pada penerapan praktis.",
      duration: "14 menit",
      category: "Video",
      icon: Video,
      gradient: "from-teal-500/10 to-cyan-500/10",
      stats: "Episode 2",
    },
  ];

  const [openIndex, setOpenIndex] = useState<number | null>(null);

  return (
    <section id="education" className="container mx-auto px-6 py-16 md:py-24 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-accent/5 to-transparent pointer-events-none" />

      <div className="relative z-10">
        {/* Enhanced Header with Better Typography */}
        <header className="text-center mb-20 space-y-6">
          <div className="inline-flex items-center px-6 py-3 rounded-full glass bg-accent/10 text-accent text-base font-medium mb-6 shadow-glass hover-lift transition-all duration-300">
            <div className="w-3 h-3 bg-accent rounded-full mr-3 animate-pulse-glow" />
            <span className="font-heading">Materi Pembelajaran</span>
          </div>
          <h2 className="space-y-3">
            <div className="font-display text-4xl md:text-6xl lg:text-7xl font-black">
              <span className="text-gradient">Edukasi & Promosi</span>
            </div>
            <div className="font-heading text-2xl md:text-4xl lg:text-5xl font-semibold text-foreground/80">
              Kesehatan Mental Santri
            </div>
          </h2>
          <p className="font-body text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Materi pendukung <span className="font-semibold text-foreground">berkualitas tinggi</span> untuk meningkatkan literasi kesehatan jiwa dalam perspektif Islam.
          </p>

          {/* Enhanced Stats with Glassmorphism */}
          <div className="flex justify-center gap-8 pt-6">
            <div className="text-center glass rounded-xl p-4 shadow-glass hover-lift transition-all duration-300 stagger-item">
              <div className="text-2xl font-bold text-primary">{items.length}</div>
              <div className="text-sm text-muted-foreground">Materi</div>
            </div>
            <div className="text-center glass rounded-xl p-4 shadow-glass hover-lift transition-all duration-300 stagger-item">
              <div className="text-2xl font-bold text-accent">100%</div>
              <div className="text-sm text-muted-foreground">Gratis</div>
            </div>
            <div className="text-center glass rounded-xl p-4 shadow-glass hover-lift transition-all duration-300 stagger-item">
              <div className="text-2xl font-bold text-emerald-600">24/7</div>
              <div className="text-sm text-muted-foreground">Akses</div>
            </div>
          </div>
        </header>

        <Separator className="mb-12" />

        {/* Enhanced Cards Grid with Varied Layouts */}
        <div className="grid gap-10 sm:grid-cols-2 lg:grid-cols-3">
          {items.map((item, idx) => (
            <Card
              key={item.title}
              className={`card-enhanced group overflow-hidden border-2 border-primary/20 glass-card bg-gradient-to-br ${item.gradient} shadow-elegant hover:shadow-glow hover:-translate-y-4 transition-all duration-700 h-full flex flex-col stagger-item ${
                idx % 3 === 0 ? 'lg:rotate-1 hover:rotate-0' :
                idx % 3 === 1 ? 'lg:-rotate-1 hover:rotate-0' :
                'lg:rotate-0.5 hover:rotate-0'
              } ${idx === 0 || idx === 2 ? 'lg:scale-105' : ''}`}
              style={{
                animationDelay: `${idx * 150}ms`,
                transformOrigin: 'center center'
              }}
            >
              {/* Card Header with Enhanced Icon */}
              <CardHeader className="pb-6">
                <div className="flex items-start gap-5">
                  <div className="flex-shrink-0 w-14 h-14 glass bg-primary/10 rounded-2xl flex items-center justify-center group-hover:bg-primary/20 transition-all duration-500 shadow-glass group-hover:scale-110">
                    <item.icon className="w-7 h-7 text-primary transition-all duration-500 group-hover:scale-125 group-hover:rotate-12" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-3">
                      <Badge variant="secondary" className="text-sm font-medium px-3 py-1">
                        {item.category}
                      </Badge>
                      <Badge variant="outline" className="text-sm font-medium px-3 py-1">
                        {item.stats}
                      </Badge>
                    </div>
                    <CardTitle className="font-heading text-xl font-bold leading-tight group-hover:text-primary transition-colors duration-500">
                      {item.title}
                    </CardTitle>
                  </div>
                </div>
              </CardHeader>

              {/* Enhanced Card Content */}
              <CardContent className="flex-1 flex flex-col gap-6">
                <p className="font-body text-base text-muted-foreground leading-relaxed">
                  {item.description}
                </p>

                {/* Enhanced Meta Information */}
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    <span className="font-medium">{item.duration}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    <span className="font-medium">Semua Level</span>
                  </div>
                </div>

                {/* Enhanced CTA Button */}
                <div className="mt-auto pt-6">
                  <MediaLightbox
                    title={item.title}
                    url={item.link}
                    kind={item.kind}
                    open={openIndex === idx}
                    onOpenChange={(o) => setOpenIndex(o ? idx : null)}
                    trigger={
                      <Button
                        onClick={() => setOpenIndex(idx)}
                        variant="outline"
                        size="lg"
                        className="btn-enhanced w-full gap-4 border-2 border-primary/30 hover:bg-primary/15 hover:border-primary/60 backdrop-blur-sm transition-all duration-500 min-h-touch-comfortable rounded-xl font-semibold text-base"
                      >
                        {item.kind === 'video' ? <Play className="w-5 h-5" /> :
                         item.kind === 'pdf' ? <Download className="w-5 h-5" /> :
                         <ExternalLink className="w-5 h-5" />}
                        <span className="font-heading">
                          {item.kind === 'video' ? 'Tonton Video' :
                           item.kind === 'pdf' ? 'Buka Modul' :
                           'Mainkan Game'}
                        </span>
                      </Button>
                    }
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Enhanced CTA Section with Glassmorphism */}
        <div className="text-center mt-16 p-8 glass-card bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl shadow-glass">
          <h3 className="text-2xl font-bold mb-4">Butuh Bantuan Lebih Lanjut?</h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Tim ahli kami siap membantu Anda memahami materi dan menerapkannya dalam kehidupan sehari-hari.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="default"
              size="lg"
              className="gap-3 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white shadow-lg hover:shadow-xl hover:shadow-primary/25 transition-all duration-300 hover:-translate-y-1 active:scale-95 focus:ring-4 focus:ring-primary/30 min-h-touch-comfortable rounded-xl"
            >
              <Users className="w-5 h-5" />
              <span className="font-semibold">Konsultasi Gratis</span>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="gap-3 border-2 border-primary/30 hover:border-primary/60 hover:bg-primary/5 backdrop-blur-sm transition-all duration-300 hover:-translate-y-0.5 active:scale-95 focus:ring-4 focus:ring-primary/20 min-h-touch-comfortable rounded-xl"
            >
              <BookOpen className="w-5 h-5" />
              <span className="font-semibold">Panduan Lengkap</span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Education;

