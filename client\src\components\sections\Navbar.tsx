import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { useGestures } from "@/hooks/use-gestures";
import logoImage from "@assets/logo_tokenpedia_nobg_1755041851408.png";
import { Menu, X, Home, Brain, BookOpen, User, Wifi, WifiOff, RefreshCw, CheckCircle, Clock, Shield, Heart, Sparkles, UserPlus, Film, Download, Gamepad2, ArrowRight } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { Link, useLocation } from "wouter";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [location] = useLocation();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const isMobile = useIsMobile();

  // Gesture support for mobile menu
  const gestureRef = useGestures({
    onSwipeDown: () => {
      if (isMobile && !isOpen) {
        setIsOpen(true);
      }
    },
    onSwipeUp: () => {
      if (isMobile && isOpen) {
        setIsOpen(false);
      }
    }
  }, { threshold: 30 });

  // Online/Offline status tracking
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const getStatusIcon = () => {
    if (!isOnline) {
      return <WifiOff className="w-4 h-4" />;
    }
    return <CheckCircle className="w-4 h-4" />;
  };

  const getStatusColor = () => {
    if (!isOnline) {
      return 'bg-red-500 text-white';
    }
    return 'bg-emerald-500 text-white';
  };

  const navItems = [
    { path: "/", label: "Beranda", icon: Home, hash: "#home" },
    { path: "/#features", label: "Fitur", icon: Sparkles, hash: "#features" },
    { path: "/assessments", label: "Assessment", icon: Brain },
    { path: "/movies", label: "Film", icon: Film },
    { path: "/downloads", label: "Download", icon: Download },
    { path: "/games", label: "Game", icon: Gamepad2 },
    { path: "/education", label: "Edukasi", icon: Heart },
  ];

  const handleNavClick = (item: typeof navItems[0]) => {
    setIsOpen(false);
    if (item.hash) {
      if (location !== "/") {
        window.location.href = item.path;
      } else {
        document.querySelector(item.hash)?.scrollIntoView({ behavior: "smooth" });
      }
    }
  };

  return (
    <header ref={gestureRef} className="w-full sticky top-0 z-50 glass-nav shadow-glass">
      <nav className="container mx-auto flex items-center justify-between h-16 px-4">
        {/* Enhanced Logo with Better Typography */}
        <Link href="/" className="flex items-center gap-4 hover:opacity-90 transition-all duration-300 hover-lift min-h-touch-comfortable">
          <div className="relative">
            <div className="glass rounded-full p-2">
              <img src={logoImage} alt="TokenPedia SantriMental Logo" className="h-12 w-12 rounded-full" />
            </div>
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-pulse-glow">
              <div className="w-2.5 h-2.5 bg-white rounded-full"></div>
            </div>
          </div>
          <span className="font-display text-2xl font-bold tracking-tight text-gradient">
            SantriMental
          </span>
        </Link>

        {/* Enhanced Desktop Navigation with Better Touch Targets */}
        <div className="hidden md:flex items-center gap-3">
          {navItems.map((item, index) => (
            <Link key={item.path} href={item.path} className="group">
              <div
                className="flex items-center gap-3 px-5 py-3 rounded-xl text-base font-medium transition-all duration-300 hover:bg-primary/10 hover:text-primary cursor-pointer glass hover:shadow-md hover-lift focus-ring stagger-item min-h-touch-comfortable"
                onClick={() => handleNavClick(item)}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <item.icon size={18} className="transition-transform duration-300 group-hover:scale-110" />
                <span className="font-heading">{item.label}</span>
              </div>
            </Link>
          ))}
        </div>

        {/* Enhanced Desktop Status & Auth Buttons */}
        <div className="hidden md:flex items-center gap-3">
          {/* Enhanced Status Indicator with Glassmorphism */}
          <div className={`px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-2 glass shadow-glass transition-all duration-300 ${getStatusColor()}`}>
            {getStatusIcon()}
            {isOnline ? 'Online' : 'Offline'}
          </div>

          {/* Enhanced Auth Buttons */}
          <Link href="/login">
            <Button variant="outline" size="sm" className="gap-2 btn-glass hover-lift transition-all duration-300 focus-ring">
              <Shield size={16} />
              Login
            </Button>
          </Link>
          <Link href="/register">
            <Button variant="default" size="sm" className="bg-button-gradient hover:shadow-glow btn-enhanced hover-lift transition-all duration-300 focus-ring">
              <UserPlus size={16} className="mr-1" />
              Daftar
            </Button>
          </Link>
        </div>

        {/* Enhanced Mobile Menu Button with Better Touch Target */}
        <Button
          variant="ghost"
          size="lg"
          className="md:hidden p-4 hover:bg-primary/10 transition-all duration-300 min-h-touch-comfortable min-w-touch-comfortable rounded-xl"
          onClick={() => setIsOpen(!isOpen)}
          data-testid="mobile-menu-toggle"
        >
          <div className="relative w-7 h-7 flex items-center justify-center">
            <div className={`absolute transition-all duration-500 ${isOpen ? 'rotate-45 translate-y-0' : 'rotate-0 -translate-y-1.5'}`}>
              <div className="w-7 h-0.5 bg-current rounded-full" />
            </div>
            <div className={`absolute transition-all duration-500 ${isOpen ? 'opacity-0 scale-0' : 'opacity-100 scale-100'}`}>
              <div className="w-7 h-0.5 bg-current rounded-full" />
            </div>
            <div className={`absolute transition-all duration-500 ${isOpen ? '-rotate-45 translate-y-0' : 'rotate-0 translate-y-1.5'}`}>
              <div className="w-7 h-0.5 bg-current rounded-full" />
            </div>
          </div>
        </Button>
      </nav>

      {/* Enhanced Mobile Menu with Better Touch Targets */}
      {isOpen && (
        <div className="md:hidden border-t glass-card shadow-glass animate-slide-up">
          <div className="container mx-auto px-6 py-8 space-y-4">
            {navItems.map((item, index) => (
              <Link key={item.path} href={item.path}>
                <div
                  className="flex items-center gap-5 px-8 py-6 rounded-2xl text-lg font-medium transition-all duration-500 glass hover:bg-primary/15 hover:text-primary cursor-pointer hover:shadow-md hover-lift active:scale-95 min-h-touch-large touch-manipulation focus-ring stagger-item group"
                  onClick={() => handleNavClick(item)}
                  data-testid={`mobile-nav-${item.label.toLowerCase()}`}
                  style={{ animationDelay: `${index * 75}ms` }}
                >
                  <div className="flex items-center justify-center w-12 h-12 rounded-2xl glass bg-primary/10 transition-all duration-500 group-hover:bg-primary/25 group-hover:scale-110">
                    <item.icon size={24} className="transition-all duration-500 group-hover:scale-125 group-hover:rotate-12" />
                  </div>
                  <span className="flex-1 font-heading">{item.label}</span>
                  <ArrowRight size={20} className="opacity-50 group-hover:opacity-100 transition-all duration-500 group-hover:translate-x-2 group-hover:scale-110" />
                </div>
              </Link>
            ))}
            <div className="pt-4 border-t border-white/10 space-y-3">
              {/* Enhanced Mobile Status Indicator */}
              <div className={`px-4 py-3 rounded-xl text-sm font-medium flex items-center justify-center gap-2 glass shadow-glass transition-all duration-300 ${getStatusColor()}`}>
                {getStatusIcon()}
                {isOnline ? 'Online' : 'Offline'}
              </div>

              {/* Enhanced Mobile Auth Buttons with Glassmorphism */}
              <div className="flex flex-col gap-4 pt-4 border-t border-white/10">
                <Link href="/login">
                  <Button
                    variant="outline"
                    size="lg"
                    className="gap-3 w-full btn-glass hover-lift transition-all duration-300 min-h-[48px] text-base focus-ring"
                    data-testid="mobile-login"
                  >
                    <Shield size={20} />
                    Login
                  </Button>
                </Link>
                <Link href="/register">
                  <Button
                    variant="default"
                    size="lg"
                    className="w-full gap-3 min-h-[48px] text-base bg-button-gradient btn-enhanced hover:shadow-glow hover-lift focus-ring"
                    data-testid="mobile-register"
                  >
                    <UserPlus size={20} />
                    Daftar Sekarang
                    <ArrowRight size={16} className="transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
