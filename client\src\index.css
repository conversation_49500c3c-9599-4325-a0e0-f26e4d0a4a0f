@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here.
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Enhanced Light Theme with Better Contrast */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Enhanced Green Brand Palette with Better Contrast */
    --primary: 108 60% 42%;
    --primary-foreground: 0 0% 100%;

    --secondary: 60 20% 88%;
    --secondary-foreground: 108 25% 20%;

    --muted: 60 15% 95%;
    --muted-foreground: 108 15% 40%;

    --accent: 100 45% 38%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 88%;
    --input: 214.3 31.8% 88%;
    --ring: 108 60% 42%;

    --radius: 0.75rem;

    /* Brand tokens - Fresh Green Theme */
    --brand: 108 60% 45%;
    --brand-2: 100 45% 40%;
    --primary-glow: 108 60% 55%;

    /* Enhanced Typography Scale */
    --font-size-xs: 0.75rem;      /* 12px */
    --font-size-sm: 0.875rem;     /* 14px */
    --font-size-base: 1rem;       /* 16px */
    --font-size-lg: 1.125rem;     /* 18px */
    --font-size-xl: 1.25rem;      /* 20px */
    --font-size-2xl: 1.5rem;      /* 24px */
    --font-size-3xl: 1.875rem;    /* 30px */
    --font-size-4xl: 2.25rem;     /* 36px */
    --font-size-5xl: 3rem;        /* 48px */
    --font-size-6xl: 3.75rem;     /* 60px */
    --font-size-7xl: 4.5rem;      /* 72px */
    --font-size-8xl: 6rem;        /* 96px */
    --font-size-9xl: 8rem;        /* 128px */

    /* Typography Weights */
    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* Line Heights */
    --line-height-none: 1;
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* Consistent Spacing System */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 2.5rem;
    --spacing-3xl: 3rem;
    --spacing-4xl: 4rem;
    --spacing-5xl: 5rem;
    --spacing-6xl: 6rem;

    /* Gradients & Shadows */
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand)) 0%, hsl(var(--brand-2)) 100%);
    --gradient-surface: radial-gradient(800px 400px at var(--mouse-x, 50%) var(--mouse-y, 50%), hsl(var(--brand) / 0.08), transparent 60%);

    --shadow-elegant: 0 10px 30px -12px hsl(var(--brand) / 0.25);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.4);

    /* Sidebar */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: var(--brand);
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 108 50% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 108 15% 20%;
    --secondary-foreground: 60 20% 90%;

    --muted: 108 10% 25%;
    --muted-foreground: 108 20% 70%;

    --accent: 100 40% 45%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 215 92% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Enhanced Animation System */
    --duration-instant: 50ms;
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;
    --duration-slower: 700ms;
    --duration-slowest: 1000ms;

    /* Easing Functions */
    --ease-linear: linear;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* Touch Target Sizes */
    --touch-target-min: 44px;
    --touch-target-comfortable: 48px;
    --touch-target-large: 56px;

    /* Gradients & Shadows */
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand-2)) 0%, hsl(var(--brand)) 100%);
    --gradient-surface: radial-gradient(800px 400px at var(--mouse-x, 50%) var(--mouse-y, 50%), hsl(var(--brand) / 0.12), transparent 60%);
    --gradient-card: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    --gradient-button: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    --shadow-elegant: 0 10px 30px -12px hsl(var(--brand) / 0.3);
    --shadow-glow: 0 0 50px hsl(var(--primary-glow) / 0.5);
    --shadow-card: 0 4px 20px -4px hsl(var(--foreground) / 0.1);
    --shadow-card-hover: 0 8px 30px -8px hsl(var(--brand) / 0.2);

    /* Animation Durations */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;

    /* Easing Functions */
    --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
    --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variation-settings: normal;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Enhanced Typography Classes */
  .font-display {
    font-family: 'Poppins', 'Inter', sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .font-heading {
    font-family: 'Poppins', 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: -0.015em;
  }

  .font-body {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    letter-spacing: -0.005em;
  }

  .font-mono {
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  }
}

@layer utilities {
  /* Enhanced Background Utilities */
  .bg-hero {
    background-image: var(--gradient-hero);
  }
  .bg-card-gradient {
    background-image: var(--gradient-card);
  }
  .bg-button-gradient {
    background-image: var(--gradient-button);
  }

  /* Glassmorphism Utilities */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  .glass-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .glass-nav {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }
  .dark .glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .dark .glass-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  .dark .glass-nav {
    background: rgba(0, 0, 0, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced Shadow Utilities */
  .shadow-elegant {
    box-shadow: var(--shadow-elegant);
  }
  .shadow-card {
    box-shadow: var(--shadow-card);
  }
  .shadow-card-hover {
    box-shadow: var(--shadow-card-hover);
  }
  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }
  .shadow-glass {
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  /* Interactive Surface with Enhanced Animation */
  .interactive-surface {
    position: relative;
    overflow: hidden;
    transition: transform var(--duration-normal) var(--ease-out-cubic);
  }
  .interactive-surface::after {
    content: "";
    position: absolute;
    inset: -1px;
    background: var(--gradient-surface);
    pointer-events: none;
    transition: opacity var(--duration-normal) var(--ease-out-cubic);
    opacity: 0;
  }
  .interactive-surface:hover::after {
    opacity: 1;
  }
  .interactive-surface:hover {
    transform: translateY(-2px);
  }

  /* Enhanced Button States with Advanced Interactions */
  .btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all var(--duration-normal) var(--ease-out-cubic);
    transform-style: preserve-3d;
  }
  .btn-enhanced::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left var(--duration-slow) var(--ease-out-cubic);
    z-index: 1;
  }
  .btn-enhanced::after {
    content: "";
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out-cubic);
    z-index: 0;
  }
  .btn-enhanced:hover::before {
    left: 100%;
  }
  .btn-enhanced:hover::after {
    opacity: 1;
  }
  .btn-enhanced:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-card-hover);
  }
  .btn-enhanced:active {
    transform: translateY(-1px) scale(0.98);
    transition-duration: var(--duration-fast);
  }
  .btn-enhanced:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Glass Button Variants */
  .btn-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--duration-normal) var(--ease-out-cubic);
  }
  .btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
  .dark .btn-glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .dark .btn-glass:hover {
    background: rgba(0, 0, 0, 0.3);
  }

  /* Card Hover Effects */
  .card-enhanced {
    transition: all var(--duration-normal) var(--ease-out-cubic);
    cursor: pointer;
  }
  .card-enhanced:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-card-hover);
  }

  /* Typography Utilities */
  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Loading Animation */
  .loading-pulse {
    animation: loading-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Bounce Animation for CTAs */
  .bounce-subtle {
    animation: bounce-subtle 2s infinite;
  }

  /* Fade In Animation */
  .fade-in {
    animation: fade-in 0.6s var(--ease-out-cubic) forwards;
  }

  /* Slide Up Animation */
  .slide-up {
    animation: slide-up 0.6s var(--ease-out-cubic) forwards;
  }

  /* Micro-interactions */
  .hover-lift {
    transition: transform var(--duration-normal) var(--ease-out-cubic);
  }
  .hover-lift:hover {
    transform: translateY(-4px);
  }

  /* Progress Indicators */
  .progress-ring {
    transform: rotate(-90deg);
    transition: stroke-dasharray var(--duration-slow) var(--ease-out-cubic);
  }
  .progress-bar {
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--accent)));
    transition: width var(--duration-slow) var(--ease-out-cubic);
  }

  /* Enhanced Focus States */
  .focus-ring {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
  .focus-ring:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Stagger Animation */
  .stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: stagger-in 0.6s var(--ease-out-cubic) forwards;
  }
  .stagger-item:nth-child(1) { animation-delay: 0ms; }
  .stagger-item:nth-child(2) { animation-delay: 100ms; }
  .stagger-item:nth-child(3) { animation-delay: 200ms; }
  .stagger-item:nth-child(4) { animation-delay: 300ms; }
  .stagger-item:nth-child(5) { animation-delay: 400ms; }
  .stagger-item:nth-child(6) { animation-delay: 500ms; }

  /* Enhanced Animation Classes */
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  .animate-skeleton-loading {
    animation: skeleton-loading 1.5s ease-in-out infinite;
    background: linear-gradient(90deg,
      hsl(var(--muted)) 25%,
      hsl(var(--muted-foreground) / 0.1) 50%,
      hsl(var(--muted)) 75%);
    background-size: 200% 100%;
  }
  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }
  .animate-scale-in {
    animation: scale-in 0.3s var(--ease-out) forwards;
  }
  .animate-wiggle {
    animation: wiggle 1s ease-in-out infinite;
  }
  .animate-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
  }
  .animate-bounce-in {
    animation: bounce-in 0.6s var(--ease-bounce) forwards;
  }
  .animate-slide-in-left {
    animation: slide-in-left 0.5s var(--ease-out-cubic) forwards;
  }
  .animate-slide-in-right {
    animation: slide-in-right 0.5s var(--ease-out-cubic) forwards;
  }
  .animate-fade-in-up {
    animation: fade-in-up 0.6s var(--ease-out-cubic) forwards;
  }

  /* Loading States */
  .loading-skeleton {
    background: linear-gradient(90deg,
      hsl(var(--muted)) 25%,
      hsl(var(--muted-foreground) / 0.1) 50%,
      hsl(var(--muted)) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s ease-in-out infinite;
    border-radius: var(--radius);
  }

  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid hsl(var(--muted));
    border-top: 2px solid hsl(var(--primary));
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-dots {
    display: inline-flex;
    gap: 4px;
  }
  .loading-dots span {
    width: 6px;
    height: 6px;
    background: hsl(var(--primary));
    border-radius: 50%;
    animation: loading-dots 1.4s ease-in-out infinite both;
  }
  .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
  .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
  .loading-dots span:nth-child(3) { animation-delay: 0s; }
}

/* Enhanced Keyframe Animations */
@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce-subtle {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes stagger-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }
  50% {
    box-shadow: 0 0 30px hsl(var(--primary) / 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Enhanced Loading Animations */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Accessibility: Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .interactive-surface::after,
  .btn-enhanced,
  .card-enhanced,
  .loading-pulse,
  .bounce-subtle,
  .fade-in,
  .slide-up {
    animation: none !important;
    transition: none !important;
  }

  .interactive-surface:hover,
  .btn-enhanced:hover,
  .card-enhanced:hover {
    transform: none !important;
  }
}
