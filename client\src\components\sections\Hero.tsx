import { But<PERSON> } from "@/components/ui/button";
import heroImg from "@assets/generated_images/Anime_female_student_assessment_dfd43af5.png";
import { useRef } from "react";
import { ArrowR<PERSON>, PlayCircle, Brain } from "lucide-react";
import { <PERSON> } from "wouter";

const Hero = () => {
  const ref = useRef<HTMLDivElement>(null);

  const onMouseMove: React.MouseEventHandler<HTMLDivElement> = (e) => {
    const el = ref.current;
    if (!el) return;
    const rect = el.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    el.style.setProperty("--mouse-x", `${x}%`);
    el.style.setProperty("--mouse-y", `${y}%`);
  };

  return (
    <section id="home" className="pt-8 md:pt-16 relative overflow-hidden min-h-screen flex items-center">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 pointer-events-none" />

      {/* Animated Background Pattern */}
      <div className="absolute inset-0 opacity-30 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_25%_25%,hsl(var(--primary)/0.1)_0%,transparent_50%)] animate-pulse" />
        <div className="absolute top-0 right-0 w-full h-full bg-[radial-gradient(circle_at_75%_25%,hsl(var(--accent)/0.1)_0%,transparent_50%)] animate-pulse" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-0 left-0 w-full h-full bg-[radial-gradient(circle_at_25%_75%,hsl(var(--primary)/0.08)_0%,transparent_50%)] animate-pulse" style={{ animationDelay: '2s' }} />
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-primary/10 rounded-full blur-3xl animate-float" />
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-accent/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }} />
      <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-primary/5 rounded-full blur-2xl animate-float" style={{ animationDelay: '2s' }} />

      <div
        ref={ref}
        onMouseMove={onMouseMove}
        className="interactive-surface glass-card rounded-2xl shadow-glass relative z-10"
      >
        <div className="container mx-auto px-6 py-12 md:py-20 grid md:grid-cols-2 gap-10 items-center">
          <div className="space-y-6 fade-in">
            {/* Enhanced Badge with Glassmorphism */}
            <div className="inline-flex items-center px-4 py-2 rounded-full glass bg-gradient-to-r from-primary/10 to-accent/10 border border-primary/20 text-primary text-sm font-medium shadow-glass hover-lift transition-all duration-300">
              <div className="w-2 h-2 bg-primary rounded-full mr-2 animate-pulse-glow" />
              TOKEN PEDIA · Kesehatan Jiwa Santri
            </div>

            {/* Enhanced Typography Hierarchy with Better Fonts */}
            <div className="space-y-8">
              <h1 className="space-y-4">
                <div className="font-display text-6xl md:text-8xl lg:text-9xl font-black leading-none tracking-tight">
                  <span className="text-gradient bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent animate-pulse-glow">
                    SantriMental
                  </span>
                </div>
                <div className="space-y-2">
                  <div className="font-heading text-2xl md:text-4xl lg:text-5xl font-semibold text-foreground/90 leading-tight">
                    Screening, Terapi, dan Edukasi
                  </div>
                  <div className="font-heading text-xl md:text-3xl lg:text-4xl font-medium text-primary">
                    Kesehatan Jiwa Santri
                  </div>
                </div>
              </h1>

              <div className="space-y-6">
                <p className="font-body text-xl md:text-2xl lg:text-3xl text-muted-foreground max-w-3xl leading-relaxed font-light">
                  Bantu santri mengenali kondisi psikologisnya melalui assessment
                  terstruktur yang telah <span className="font-medium text-foreground">tervalidasi klinis</span>.
                </p>

                <div className="glass rounded-xl p-4 max-w-2xl shadow-glass">
                  <div className="text-sm md:text-base text-muted-foreground/90">
                    <span className="font-semibold text-foreground block mb-2">🧠 Assessment Tools Tersedia:</span>
                    <div className="flex flex-wrap gap-2">
                      {['MHKQ', 'PDD', 'GSE', 'MSCS', 'SRQ-20', 'DASS-42'].map((tool) => (
                        <span key={tool} className="font-mono text-xs md:text-sm bg-primary/10 text-primary px-3 py-1 rounded-full border border-primary/20 hover:bg-primary/20 transition-colors duration-300">
                          {tool}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced CTA Buttons with Advanced Interactions */}
            <div className="flex flex-col sm:flex-row gap-6 pt-8">
              <Link href="/assessments">
                <Button
                  variant="default"
                  size="lg"
                  className="btn-enhanced group w-full sm:w-auto gap-4 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white text-xl px-10 py-6 h-auto rounded-2xl shadow-elegant hover:shadow-glow transition-all duration-500 min-h-[var(--touch-target-large)] font-semibold"
                  data-testid="button-start-assessment"
                >
                  <Brain className="w-7 h-7 transition-all duration-500 group-hover:scale-125 group-hover:rotate-12" />
                  <span className="font-display">Mulai Assessment</span>
                  <ArrowRight className="w-6 h-6 transition-all duration-500 group-hover:translate-x-2 group-hover:scale-125" />
                </Button>
              </Link>
              <Button
                variant="outline"
                size="lg"
                className="btn-enhanced group w-full sm:w-auto gap-4 border-2 border-primary/40 hover:border-primary/80 hover:bg-primary/10 text-xl px-10 py-6 h-auto rounded-2xl backdrop-blur-sm transition-all duration-500 min-h-[var(--touch-target-large)] font-semibold glass"
                onClick={() => document.querySelector("#education")?.scrollIntoView({ behavior: "smooth" })}
                data-testid="button-explore-education"
              >
                <PlayCircle className="w-7 h-7 transition-all duration-500 group-hover:scale-125 group-hover:rotate-12" />
                <span className="font-heading">Jelajahi Edukasi</span>
              </Button>
            </div>

            {/* Enhanced Trust Indicators */}
            <div className="flex flex-wrap items-center gap-6 pt-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-2 stagger-item">
                <div className="w-3 h-3 bg-emerald-500 rounded-full animate-pulse-glow" />
                <span>Tervalidasi Klinis</span>
              </div>
              <div className="flex items-center gap-2 stagger-item">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse-glow" />
                <span>Mobile-First Design</span>
              </div>
              <div className="flex items-center gap-2 stagger-item">
                <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse-glow" />
                <span>Data Aman</span>
              </div>
            </div>
          </div>
          {/* Enhanced Hero Image */}
          <div className="relative slide-up">
            {/* Decorative Elements */}
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-primary/20 rounded-full blur-2xl animate-pulse" />
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-accent/20 rounded-full blur-2xl animate-pulse delay-500" />

            {/* Enhanced Main Image Container with Glassmorphism */}
            <div className="relative glass-card bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl p-2 shadow-glass hover:shadow-card-hover transition-all duration-500">
              <img
                src={heroImg}
                alt="Ilustrasi anime pesantren mental health - santri Muslim dengan teknologi kesehatan jiwa"
                loading="eager"
                className="w-full h-auto rounded-xl shadow-lg hover:scale-105 transition-transform duration-500 ease-out"
              />

              {/* Enhanced Floating Elements with Glassmorphism */}
              <div className="absolute top-4 right-4 glass rounded-full p-3 shadow-glass animate-float hover-lift">
                <Brain className="w-6 h-6 text-primary" />
              </div>

              <div className="absolute bottom-4 left-4 glass rounded-full px-4 py-2 shadow-glass hover-lift">
                <span className="text-sm font-medium text-primary">AI-Powered</span>
              </div>
            </div>

            {/* Enhanced Stats Cards with Glassmorphism */}
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex gap-4">
              <div className="glass rounded-xl px-4 py-2 shadow-glass border border-primary/10 hover-lift transition-all duration-300 stagger-item">
                <div className="text-center">
                  <div className="text-lg font-bold text-primary">6+</div>
                  <div className="text-xs text-muted-foreground">Assessment Tools</div>
                </div>
              </div>
              <div className="glass rounded-xl px-4 py-2 shadow-glass border border-accent/10 hover-lift transition-all duration-300 stagger-item">
                <div className="text-center">
                  <div className="text-lg font-bold text-accent">100%</div>
                  <div className="text-xs text-muted-foreground">Mobile Ready</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
