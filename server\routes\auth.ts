/**
 * 🔐 Authentication Routes
 * 
 * Handles user registration, login, and authentication
 */

import { Router } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { ConnectionManager } from '../database/connection-manager.js';
import { 
  authenticateToken, 
  generateToken, 
  hashPassword, 
  comparePassword,
  getUserPermissions,
  AuthenticatedRequest 
} from '../middleware/auth.js';

const router = Router();

/**
 * User Registration
 */
router.post('/signup', async (req, res) => {
  try {
    const { email, password, namaLengkap, role = 'user' } = req.body;

    // Validation
    if (!email || !password || !namaLengkap) {
      return res.status(400).json({
        success: false,
        error: 'Email, password, and nama lengkap are required'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        error: 'Password must be at least 6 characters long'
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid email format'
      });
    }

    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();
    const provider = connectionManager.getCurrentProvider();

    // Check if user already exists
    let existingUser;
    if (provider === 'sqlite') {
      const stmt = connection.db.prepare('SELECT id FROM users WHERE email = ?');
      existingUser = stmt.get(email);
    } else {
      const result = await connection.getPool().execute(
        'SELECT id FROM users WHERE email = ?',
        [email]
      );
      existingUser = (result as any)[0][0];
    }

    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'User with this email already exists'
      });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);
    const userId = uuidv4();
    const profileId = uuidv4();
    const now = new Date().toISOString();

    // Create user (using actual database structure)
    let newUserId;
    if (provider === 'sqlite') {
      const userStmt = connection.db.prepare(`
        INSERT INTO users (username, email, password_hash, first_name, role, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 'active', datetime('now'), datetime('now'))
      `);
      const result = userStmt.run(email.split('@')[0], email, hashedPassword, namaLengkap, role);
      newUserId = result.lastInsertRowid;
    } else {
      // MySQL
      const result = await connection.getPool().execute(`
        INSERT INTO users (username, email, password_hash, first_name, role, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())
      `, [email.split('@')[0], email, hashedPassword, namaLengkap, role]);
      newUserId = (result as any)[0].insertId;
    }

    // Generate token
    const token = generateToken({ id: newUserId.toString(), email, role });

    // Get user permissions
    const permissions = await getUserPermissions(newUserId.toString());

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: {
          id: newUserId,
          email,
          role,
          namaLengkap,
          isActive: true,
          emailVerified: false,
          permissions
        },
        token
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Registration failed'
    });
  }
});

/**
 * User Login
 */
router.post('/signin', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();
    const provider = connectionManager.getCurrentProvider();

    // Get user
    let user;
    if (provider === 'sqlite') {
      const stmt = connection.db.prepare(`
        SELECT id, username, email, password_hash, first_name, role, status, email_verified_at
        FROM users
        WHERE email = ? AND status = 'active'
      `);
      user = stmt.get(email);
    } else {
      const result = await connection.getPool().execute(`
        SELECT id, username, email, password_hash, first_name, role, status, email_verified_at
        FROM users
        WHERE email = ? AND status = 'active'
      `, [email]);
      user = (result as any)[0][0];
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid email or password'
      });
    }

    // Check password
    const isValidPassword = await comparePassword(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        error: 'Invalid email or password'
      });
    }

    // Update last login
    if (provider === 'sqlite') {
      const updateStmt = connection.db.prepare('UPDATE users SET last_login_at = datetime("now") WHERE id = ?');
      updateStmt.run(user.id);
    } else {
      await connection.getPool().execute(
        'UPDATE users SET last_login_at = NOW() WHERE id = ?',
        [user.id]
      );
    }

    // Generate token
    const token = generateToken({ id: user.id.toString(), email: user.email, role: user.role });

    // Get user permissions
    const permissions = await getUserPermissions(user.id.toString());

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          namaLengkap: user.first_name,
          isActive: user.status === 'active',
          emailVerified: Boolean(user.email_verified_at),
          permissions
        },
        token
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Login failed'
    });
  }
});

/**
 * Get Current User Profile
 */
router.get('/profile', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const userId = (req as AuthenticatedRequest).user!.id;
    
    const connectionManager = ConnectionManager.getInstance();
    const connection = await connectionManager.getConnection();
    const provider = connectionManager.getCurrentProvider();

    let userProfile;
    if (provider === 'sqlite') {
      const stmt = connection.db.prepare(`
        SELECT id, username, email, first_name, last_name, phone, avatar,
               role, status, email_verified_at, last_login_at, created_at, updated_at
        FROM users
        WHERE id = ?
      `);
      userProfile = stmt.get(userId);
    } else {
      const result = await connection.getPool().execute(`
        SELECT id, username, email, first_name, last_name, phone, avatar,
               role, status, email_verified_at, last_login_at, created_at, updated_at
        FROM users
        WHERE id = ?
      `, [userId]);
      userProfile = (result as any)[0][0];
    }

    if (!userProfile) {
      return res.status(404).json({
        success: false,
        error: 'User profile not found'
      });
    }

    // Get user permissions
    const permissions = await getUserPermissions(userId);

    // Convert SQLite integer booleans
    if (provider === 'sqlite') {
      userProfile.is_active = Boolean(userProfile.is_active);
      userProfile.email_verified = Boolean(userProfile.email_verified);
    }

    // Remove password from response
    delete userProfile.password_hash;

    res.json({
      success: true,
      data: {
        ...userProfile,
        permissions
      }
    });

  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch profile'
    });
  }
});

/**
 * Logout (for future session management)
 */
router.post('/logout', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    // For now, just return success
    // In the future, we can invalidate the token or manage sessions
    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Logout failed'
    });
  }
});

export default router;
