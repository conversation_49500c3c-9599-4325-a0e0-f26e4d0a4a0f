import React from "react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>rkles } from "lucide-react";

interface LoadingProps {
  variant?: "default" | "pulse" | "dots" | "brain" | "shimmer";
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  text?: string;
}

const Loading = ({ 
  variant = "default", 
  size = "md", 
  className,
  text 
}: LoadingProps) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8", 
    lg: "w-12 h-12",
    xl: "w-16 h-16"
  };

  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base",
    xl: "text-lg"
  };

  if (variant === "pulse") {
    return (
      <div className={cn("flex flex-col items-center justify-center gap-3", className)}>
        <div className="relative">
          <div className={cn(
            "rounded-full bg-primary/20 animate-pulse",
            sizeClasses[size]
          )} />
          <div className={cn(
            "absolute inset-0 rounded-full bg-primary animate-ping",
            sizeClasses[size]
          )} />
          <div className={cn(
            "absolute inset-2 rounded-full bg-primary",
            size === "sm" ? "inset-1" : size === "lg" ? "inset-3" : size === "xl" ? "inset-4" : "inset-2"
          )} />
        </div>
        {text && (
          <p className={cn("text-muted-foreground animate-pulse", textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    );
  }

  if (variant === "dots") {
    return (
      <div className={cn("flex flex-col items-center justify-center gap-3", className)}>
        <div className="flex gap-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                "rounded-full bg-primary animate-bounce",
                size === "sm" ? "w-2 h-2" : size === "lg" ? "w-4 h-4" : size === "xl" ? "w-5 h-5" : "w-3 h-3"
              )}
              style={{ animationDelay: `${i * 0.1}s` }}
            />
          ))}
        </div>
        {text && (
          <p className={cn("text-muted-foreground", textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    );
  }

  if (variant === "brain") {
    return (
      <div className={cn("flex flex-col items-center justify-center gap-4", className)}>
        <div className="relative">
          <div className="glass rounded-full p-4 shadow-glass">
            <Brain className={cn(
              "text-primary animate-pulse-glow",
              sizeClasses[size]
            )} />
          </div>
          <div className="absolute -top-1 -right-1">
            <Heart className="w-6 h-6 text-accent animate-heartbeat" />
          </div>
          <div className="absolute inset-0 rounded-full bg-primary/10 animate-ping" />
        </div>
        {text && (
          <p className={cn("font-heading text-muted-foreground animate-pulse", textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    );
  }

  if (variant === "shimmer") {
    return (
      <div className={cn("flex flex-col items-center justify-center gap-3", className)}>
        <div className={cn(
          "rounded-lg bg-gradient-to-r from-muted via-muted/50 to-muted animate-shimmer bg-[length:200px_100%]",
          size === "sm" ? "w-16 h-4" : size === "lg" ? "w-32 h-8" : size === "xl" ? "w-40 h-10" : "w-24 h-6"
        )} />
        {text && (
          <div className={cn(
            "rounded bg-gradient-to-r from-muted via-muted/50 to-muted animate-shimmer bg-[length:200px_100%]",
            size === "sm" ? "w-20 h-3" : size === "lg" ? "w-36 h-5" : size === "xl" ? "w-44 h-6" : "w-28 h-4"
          )} />
        )}
      </div>
    );
  }

  // Default spinner
  return (
    <div className={cn("flex flex-col items-center justify-center gap-3", className)}>
      <Loader2 className={cn(
        "animate-spin text-primary",
        sizeClasses[size]
      )} />
      {text && (
        <p className={cn("text-muted-foreground", textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  );
};

// Skeleton component for content loading
interface SkeletonProps {
  className?: string;
  variant?: "text" | "circular" | "rectangular" | "card";
}

const Skeleton = ({ className, variant = "rectangular" }: SkeletonProps) => {
  const baseClasses = "animate-pulse bg-muted";
  
  const variantClasses = {
    text: "h-4 w-full rounded",
    circular: "rounded-full",
    rectangular: "rounded-md",
    card: "rounded-lg h-48 w-full"
  };

  return (
    <div className={cn(
      baseClasses,
      variantClasses[variant],
      className
    )} />
  );
};

// Loading overlay component
interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  variant?: LoadingProps["variant"];
}

const LoadingOverlay = ({ 
  isLoading, 
  children, 
  loadingText = "Loading...",
  variant = "default"
}: LoadingOverlayProps) => {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
          <Loading variant={variant} text={loadingText} />
        </div>
      )}
    </div>
  );
};

// Enhanced Mobile Loading Component
interface MobileLoadingProps {
  isVisible: boolean;
  message?: string;
  progress?: number;
}

const MobileLoading = ({
  isVisible,
  message = "Memuat...",
  progress
}: MobileLoadingProps) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-background/90 backdrop-blur-sm z-50 flex items-center justify-center p-6">
      <div className="glass-card p-8 rounded-2xl shadow-glass text-center space-y-6 max-w-sm w-full">
        <div className="relative">
          <div className="w-16 h-16 mx-auto glass rounded-full flex items-center justify-center shadow-glass">
            <Brain className="w-8 h-8 text-primary animate-pulse-glow" />
          </div>
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-bounce-subtle">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
        </div>

        <div className="space-y-3">
          <div className="loading-dots justify-center" />
          <p className="font-heading text-lg text-foreground">
            {message}
          </p>
        </div>

        {progress !== undefined && (
          <div className="space-y-2">
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-primary to-accent rounded-full transition-all duration-500 ease-out"
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              {Math.round(progress || 0)}%
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

// Pull to Refresh Loading
interface PullToRefreshProps {
  isRefreshing: boolean;
  onRefresh: () => void;
  children: React.ReactNode;
  threshold?: number;
}

const PullToRefresh = ({
  isRefreshing,
  onRefresh,
  children,
  threshold = 80
}: PullToRefreshProps) => {
  const [pullDistance, setPullDistance] = React.useState(0);
  const [isPulling, setIsPulling] = React.useState(false);
  const startY = React.useRef(0);

  const handleTouchStart = (e: React.TouchEvent) => {
    if (window.scrollY === 0) {
      startY.current = e.touches[0].clientY;
      setIsPulling(true);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isPulling || window.scrollY > 0) return;

    const currentY = e.touches[0].clientY;
    const distance = Math.max(0, currentY - startY.current);
    setPullDistance(Math.min(distance, threshold * 1.5));
  };

  const handleTouchEnd = () => {
    if (pullDistance >= threshold && !isRefreshing) {
      onRefresh();
    }
    setIsPulling(false);
    setPullDistance(0);
  };

  return (
    <div
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      className="relative"
    >
      {/* Pull to Refresh Indicator */}
      <div
        className="absolute top-0 left-0 right-0 flex items-center justify-center transition-all duration-300 z-10"
        style={{
          transform: `translateY(${pullDistance - threshold}px)`,
          opacity: pullDistance > 20 ? 1 : 0
        }}
      >
        <div className="glass rounded-full p-3 shadow-glass">
          <div
            className={cn(
              "transition-transform duration-300",
              pullDistance >= threshold ? "rotate-180" : "rotate-0"
            )}
          >
            {isRefreshing ? (
              <Loading variant="default" size="sm" />
            ) : (
              <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full" />
            )}
          </div>
        </div>
      </div>

      <div
        className="transition-transform duration-300"
        style={{ transform: `translateY(${Math.min(pullDistance, threshold)}px)` }}
      >
        {children}
      </div>
    </div>
  );
};

export { Loading, Skeleton, LoadingOverlay, MobileLoading, PullToRefresh };
export type { LoadingProps, SkeletonProps, LoadingOverlayProps, MobileLoadingProps, PullToRefreshProps };
