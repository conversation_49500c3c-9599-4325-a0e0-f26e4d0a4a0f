#!/usr/bin/env node

/**
 * 🔍 Verify Migration Results
 * 
 * Checks if the Aiven MySQL migration was successful
 */

import 'dotenv/config';
import mysql from 'mysql2/promise';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(`🔍 ${message}`, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function verifyMigration() {
  logHeader('Aiven MySQL Migration Verification');
  
  const connection = await mysql.createConnection(process.env.AIVEN_MYSQL_URL);
  
  try {
    // Test connection
    await connection.ping();
    logSuccess('Connected to Aiven MySQL successfully');
    
    // Check users table structure
    logInfo('Checking users table structure...');
    const [columns] = await connection.execute("SHOW COLUMNS FROM users");
    
    const expectedColumns = ['role', 'is_active', 'last_login_at', 'email_verified', 'email_verified_at'];
    const existingColumns = columns.map(col => col.Field);
    
    for (const expectedCol of expectedColumns) {
      if (existingColumns.includes(expectedCol)) {
        logSuccess(`Column '${expectedCol}' exists`);
      } else {
        logError(`Column '${expectedCol}' is missing`);
      }
    }
    
    // Check indexes
    logInfo('Checking indexes...');
    const [indexes] = await connection.execute("SHOW INDEX FROM users");
    const indexNames = indexes.map(idx => idx.Key_name);
    
    const expectedIndexes = ['idx_users_role', 'idx_users_active', 'idx_users_email_verified'];
    for (const expectedIdx of expectedIndexes) {
      if (indexNames.includes(expectedIdx)) {
        logSuccess(`Index '${expectedIdx}' exists`);
      } else {
        logError(`Index '${expectedIdx}' is missing`);
      }
    }
    
    // Check admin user
    logInfo('Checking admin user...');
    const [adminUsers] = await connection.execute("SELECT id, email, role FROM users WHERE role = 'admin'");
    
    if (adminUsers.length > 0) {
      logSuccess(`Found ${adminUsers.length} admin user(s)`);
      for (const admin of adminUsers) {
        logInfo(`  - ${admin.email} (ID: ${admin.id})`);
      }
    } else {
      logError('No admin users found');
    }
    
    // Check total users
    const [userCount] = await connection.execute("SELECT COUNT(*) as count FROM users");
    logInfo(`Total users in database: ${userCount[0].count}`);
    
    // Check user roles distribution
    const [roleStats] = await connection.execute("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    logInfo('User roles distribution:');
    for (const stat of roleStats) {
      logInfo(`  - ${stat.role}: ${stat.count} users`);
    }
    
    logSuccess('Migration verification completed successfully!');
    
  } catch (error) {
    logError(`Verification failed: ${error.message}`);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run verification
verifyMigration().catch(error => {
  console.error('Verification failed:', error);
  process.exit(1);
});
