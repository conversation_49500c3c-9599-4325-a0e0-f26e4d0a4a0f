import React from 'react';
import { cn } from '@/lib/utils';
import { CheckCircle, AlertCircle, Info, X, Loader2 } from 'lucide-react';
import { Button } from './button';

export interface ToastProps {
  id?: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'success' | 'error' | 'warning' | 'info' | 'loading';
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  onClose?: () => void;
  className?: string;
}

export const EnhancedToast: React.FC<ToastProps> = ({
  title,
  description,
  variant = 'default',
  action,
  onClose,
  className
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return {
          container: 'bg-emerald-50 border-emerald-200 text-emerald-900',
          icon: <CheckCircle className="w-5 h-5 text-emerald-600" />,
          iconBg: 'bg-emerald-100'
        };
      case 'error':
        return {
          container: 'bg-red-50 border-red-200 text-red-900',
          icon: <AlertCircle className="w-5 h-5 text-red-600" />,
          iconBg: 'bg-red-100'
        };
      case 'warning':
        return {
          container: 'bg-yellow-50 border-yellow-200 text-yellow-900',
          icon: <AlertCircle className="w-5 h-5 text-yellow-600" />,
          iconBg: 'bg-yellow-100'
        };
      case 'info':
        return {
          container: 'bg-blue-50 border-blue-200 text-blue-900',
          icon: <Info className="w-5 h-5 text-blue-600" />,
          iconBg: 'bg-blue-100'
        };
      case 'loading':
        return {
          container: 'bg-primary/5 border-primary/20 text-foreground',
          icon: <Loader2 className="w-5 h-5 text-primary animate-spin" />,
          iconBg: 'bg-primary/10'
        };
      default:
        return {
          container: 'bg-background border-border text-foreground',
          icon: <Info className="w-5 h-5 text-muted-foreground" />,
          iconBg: 'bg-muted'
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <div className={cn(
      "relative flex items-start gap-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm animate-scale-in",
      styles.container,
      className
    )}>
      {/* Icon */}
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        styles.iconBg
      )}>
        {styles.icon}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        {title && (
          <h4 className="font-semibold text-sm mb-1">
            {title}
          </h4>
        )}
        {description && (
          <p className="text-sm opacity-90">
            {description}
          </p>
        )}
        
        {/* Action Button */}
        {action && (
          <div className="mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={action.onClick}
              className="h-8 px-3 text-xs"
            >
              {action.label}
            </Button>
          </div>
        )}
      </div>

      {/* Close Button */}
      {onClose && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="flex-shrink-0 w-8 h-8 p-0 hover:bg-black/10"
        >
          <X className="w-4 h-4" />
        </Button>
      )}
    </div>
  );
};

// Toast Container for managing multiple toasts
interface ToastContainerProps {
  toasts: ToastProps[];
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

export const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  position = 'top-right'
}) => {
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-center':
        return 'top-4 left-1/2 -translate-x-1/2';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-center':
        return 'bottom-4 left-1/2 -translate-x-1/2';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'top-4 right-4';
    }
  };

  return (
    <div className={cn(
      "fixed z-50 flex flex-col gap-2 max-w-sm w-full",
      getPositionClasses()
    )}>
      {toasts.map((toast, index) => (
        <div
          key={toast.id || index}
          className="animate-slide-up"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <EnhancedToast {...toast} />
        </div>
      ))}
    </div>
  );
};

// Notification Badge Component
interface NotificationBadgeProps {
  count: number;
  max?: number;
  variant?: 'default' | 'primary' | 'destructive';
  className?: string;
  children: React.ReactNode;
}

export const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  count,
  max = 99,
  variant = 'destructive',
  className,
  children
}) => {
  const displayCount = count > max ? `${max}+` : count.toString();
  
  const variantClasses = {
    default: 'bg-muted text-muted-foreground',
    primary: 'bg-primary text-primary-foreground',
    destructive: 'bg-destructive text-destructive-foreground'
  };

  return (
    <div className={cn("relative inline-block", className)}>
      {children}
      {count > 0 && (
        <span className={cn(
          "absolute -top-2 -right-2 flex items-center justify-center min-w-5 h-5 text-xs font-bold rounded-full animate-scale-in",
          variantClasses[variant]
        )}>
          {displayCount}
        </span>
      )}
    </div>
  );
};

// Status Indicator Component
interface StatusIndicatorProps {
  status: 'online' | 'offline' | 'busy' | 'away';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'md',
  showLabel = false,
  className
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  const statusConfig = {
    online: { color: 'bg-emerald-500', label: 'Online', animate: 'animate-pulse' },
    offline: { color: 'bg-gray-400', label: 'Offline', animate: '' },
    busy: { color: 'bg-red-500', label: 'Busy', animate: 'animate-pulse' },
    away: { color: 'bg-yellow-500', label: 'Away', animate: '' }
  };

  const config = statusConfig[status];

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className={cn(
        "rounded-full",
        sizeClasses[size],
        config.color,
        config.animate
      )} />
      {showLabel && (
        <span className="text-sm text-muted-foreground">
          {config.label}
        </span>
      )}
    </div>
  );
};

export default EnhancedToast;
