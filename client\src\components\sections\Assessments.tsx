import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/hooks/use-toast";
import { CheckCircle, Clock, ExternalLink, Brain, FileText, BarChart3 } from "lucide-react";

const Assessments = () => {
  const items = [
    {
      key: "MHKQ",
      title: "MHKQ (Mental Health Knowledge)",
      description: "Kuesioner untuk mengukur pengetahuan kesehatan mental",
      duration: "10-15 menit",
      questions: 25,
      link: "",
      ready: false,
      icon: Brain,
      gradient: "from-blue-500/10 to-indigo-500/10",
      borderColor: "border-blue-200/50"
    },
    {
      key: "PDD",
      title: "PDD (Perceived Devaluation Discrimination)",
      description: "Mengukur persepsi stigma dan diskriminasi kesehatan mental",
      duration: "5-8 menit",
      questions: 12,
      link: "https://docs.google.com/document/d/1XHCibeQfIneNOLPaNmwMmnuceasnoeWh/edit?usp=sharing",
      ready: true,
      icon: BarChart3,
      gradient: "from-emerald-500/10 to-green-500/10",
      borderColor: "border-emerald-200/50"
    },
    {
      key: "GSE",
      title: "GSE (General Self-Efficacy)",
      description: "Menilai keyakinan diri dalam menghadapi tantangan",
      duration: "5-7 menit",
      questions: 10,
      link: "",
      ready: false,
      icon: CheckCircle,
      gradient: "from-purple-500/10 to-pink-500/10",
      borderColor: "border-purple-200/50"
    },
    {
      key: "MSCS",
      title: "MSCS (Mindful Self-Care Scale)",
      description: "Mengukur praktik perawatan diri yang mindful",
      duration: "8-12 menit",
      questions: 33,
      link: "",
      ready: false,
      icon: FileText,
      gradient: "from-orange-500/10 to-red-500/10",
      borderColor: "border-orange-200/50"
    },
    {
      key: "SRQ20",
      title: "SRQ-20 (Self-Reporting Questionnaire)",
      description: "Skrining gangguan mental umum dan gejala psikologis",
      duration: "8-10 menit",
      questions: 20,
      link: "https://docs.google.com/document/d/1STjaeoqCylbdQShxkjg9FXYgzROvd1dW/edit?usp=sharing",
      ready: true,
      icon: Brain,
      gradient: "from-teal-500/10 to-cyan-500/10",
      borderColor: "border-teal-200/50"
    },
    {
      key: "DASS42",
      title: "DASS-42 (Depresi, Kecemasan, Stres)",
      description: "Mengukur tingkat depresi, kecemasan, dan stres",
      duration: "10-15 menit",
      questions: 42,
      link: "https://docs.google.com/document/d/13ki5rVNE__H1Fd2UWU3eb_BaTyv6GRVT/edit?usp=sharing",
      ready: true,
      icon: BarChart3,
      gradient: "from-rose-500/10 to-pink-500/10",
      borderColor: "border-rose-200/50"
    },
  ];

  const handleStart = (it: (typeof items)[number]) => {
    if (it.link) {
      window.open(it.link, "_blank");
      return;
    }
    toast({
      title: `${it.title}`,
      description: "Link akan ditambahkan. Assessment in-app akan aktif setelah backend tersedia.",
    });
  };

  const completedAssessments = items.filter(item => item.ready).length;
  const totalAssessments = items.length;
  const progressPercentage = (completedAssessments / totalAssessments) * 100;

  return (
    <section id="assessments" className="container mx-auto px-6 py-16 md:py-24 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent pointer-events-none" />

      <div className="relative z-10">
        {/* Enhanced Header */}
        <header className="text-center mb-16 space-y-4">
          <div className="inline-flex items-center px-4 py-2 rounded-full glass bg-primary/10 text-primary text-sm font-medium mb-4 shadow-glass hover-lift transition-all duration-300">
            <div className="w-2 h-2 bg-primary rounded-full mr-2 animate-pulse-glow" />
            Assessment Tools
          </div>
          <h2 className="text-3xl md:text-5xl font-bold">
            <span className="text-gradient">Skrining Kesehatan Mental</span>
            <br />
            <span className="text-foreground/80 text-2xl md:text-3xl font-semibold">
              Tervalidasi & Terpercaya
            </span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Mulai perjalanan pemahaman diri dengan kuesioner yang telah tervalidasi secara klinis dan digunakan oleh profesional kesehatan mental.
          </p>

          {/* Progress Overview */}
          <div className="glass rounded-2xl p-6 max-w-md mx-auto shadow-glass">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-muted-foreground">Progress Ketersediaan</span>
              <span className="text-sm font-bold text-primary">{completedAssessments}/{totalAssessments}</span>
            </div>
            <Progress value={progressPercentage} className="h-2 mb-2" />
            <p className="text-xs text-muted-foreground">
              {completedAssessments} assessment siap digunakan
            </p>
          </div>
        </header>

        {/* Enhanced Cards Grid */}
        <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {items.map((item, index) => (
            <Card
              key={item.key}
              className={`card-enhanced group overflow-hidden border-2 ${item.borderColor} glass-card bg-gradient-to-br ${item.gradient} shadow-glass hover:shadow-card-hover h-full flex flex-col stagger-item`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Card Header with Icon and Status */}
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0 w-12 h-12 glass rounded-xl flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-300">
                      <item.icon className="w-6 h-6 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg font-bold leading-tight group-hover:text-primary transition-colors duration-300">
                        {item.title}
                      </CardTitle>
                    </div>
                  </div>
                  <Badge
                    variant={item.ready ? "default" : "secondary"}
                    className={`flex items-center gap-1 ${item.ready ? 'bg-emerald-500 hover:bg-emerald-600' : 'bg-orange-500 hover:bg-orange-600'}`}
                  >
                    {item.ready ? <CheckCircle className="w-3 h-3" /> : <Clock className="w-3 h-3" />}
                    {item.ready ? 'Siap' : 'Segera'}
                  </Badge>
                </div>
              </CardHeader>

              {/* Card Content */}
              <CardContent className="flex-1 flex flex-col gap-4">
                <p className="text-muted-foreground leading-relaxed text-sm">
                  {item.description}
                </p>

                {/* Meta Information */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {item.duration}
                  </div>
                  <div className="flex items-center gap-1">
                    <FileText className="w-3 h-3" />
                    {item.questions} pertanyaan
                  </div>
                </div>

                {/* Progress Bar for Ready Items */}
                {item.ready && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Kelengkapan</span>
                      <span className="text-primary font-medium">100%</span>
                    </div>
                    <Progress value={100} className="h-1" />
                  </div>
                )}

                {/* CTA Button */}
                <div className="mt-auto pt-4">
                  <Button
                    variant={item.ready ? "default" : "outline"}
                    size="lg"
                    onClick={() => handleStart(item)}
                    className={`w-full gap-3 transition-all duration-300 hover-lift focus-ring ${
                      item.ready
                        ? 'bg-button-gradient hover:shadow-glow btn-enhanced'
                        : 'btn-glass border-2 border-primary/20 hover:border-primary/40'
                    }`}
                  >
                    {item.ready ? <ExternalLink className="w-4 h-4" /> : <Clock className="w-4 h-4" />}
                    {item.ready ? "Mulai Assessment" : "Segera Tersedia"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16 p-8 glass-card rounded-2xl shadow-glass">
          <h3 className="text-2xl font-bold mb-4">Butuh Bantuan Interpretasi?</h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            Hasil assessment akan lebih bermakna dengan bimbingan profesional. Konsultasikan hasil Anda dengan ahli kesehatan mental.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="default" size="lg" className="gap-2 btn-enhanced bg-button-gradient hover:shadow-glow hover-lift">
              <Brain className="w-5 h-5" />
              Konsultasi Profesional
            </Button>
            <Button variant="outline" size="lg" className="gap-2 btn-glass hover-lift">
              <FileText className="w-5 h-5" />
              Panduan Interpretasi
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Assessments;
